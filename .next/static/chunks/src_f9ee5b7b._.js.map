{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/auth/LoginButton.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { LogIn, LogOut, User } from 'lucide-react'\n\nexport default function LoginButton() {\n  const { user, signInWithGoogle, signOut, loading } = useAuth()\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleSignIn = async () => {\n    setIsLoading(true)\n    try {\n      await signInWithGoogle()\n    } catch (error) {\n      console.error('Sign in error:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleSignOut = async () => {\n    setIsLoading(true)\n    try {\n      await signOut()\n    } catch (error) {\n      console.error('Sign out error:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"animate-pulse bg-gray-200 rounded-md h-10 w-24\"></div>\n    )\n  }\n\n  if (user) {\n    return null // Account button will be handled by AccountButton component\n  }\n\n  return (\n    <button\n      onClick={handleSignIn}\n      disabled={isLoading}\n      className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n    >\n      {isLoading ? (\n        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n      ) : (\n        <>\n          <LogIn className=\"w-4 h-4 mr-2\" />\n          Sign in with Google\n        </>\n      )}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe;QACnB,aAAa;QACb,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB;QACpB,aAAa;QACb,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;;;;;IAEnB;IAEA,IAAI,MAAM;QACR,OAAO,KAAK,4DAA4D;;IAC1E;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,UAAU;QACV,WAAU;kBAET,0BACC,6LAAC;YAAI,WAAU;;;;;iCAEf;;8BACE,6LAAC,2MAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;gBAAiB;;;;;;;;AAM5C;GApDwB;;QAC+B,kIAAA,CAAA,UAAO;;;KADtC", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/auth/AccountButton.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useRouter } from 'next/navigation'\nimport { User, Settings, LogOut, ChevronDown } from 'lucide-react'\n\nexport default function AccountButton() {\n  const { user, signOut, loading } = useAuth()\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false)\n  const [isLoading, setIsLoading] = useState(false)\n  const dropdownRef = useRef<HTMLDivElement>(null)\n  const router = useRouter()\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    function handleClickOutside(event: MouseEvent) {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsDropdownOpen(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside)\n    }\n  }, [])\n\n  const handleSignOut = async () => {\n    setIsLoading(true)\n    try {\n      await signOut()\n      setIsDropdownOpen(false)\n      router.push('/')\n    } catch (error) {\n      console.error('Sign out error:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleProfileClick = () => {\n    setIsDropdownOpen(false)\n    router.push('/profile')\n  }\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word.charAt(0))\n      .join('')\n      .toUpperCase()\n      .slice(0, 2)\n  }\n\n  if (loading || !user) {\n    return null\n  }\n\n  return (\n    <div className=\"relative\" ref={dropdownRef}>\n      <button\n        onClick={() => setIsDropdownOpen(!isDropdownOpen)}\n        className=\"flex items-center space-x-2 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n      >\n        {user.avatar_url ? (\n          <img\n            src={user.avatar_url}\n            alt={user.display_name}\n            className=\"w-8 h-8 rounded-full object-cover\"\n          />\n        ) : (\n          <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium\">\n            {getInitials(user.display_name)}\n          </div>\n        )}\n        <span className=\"hidden sm:block\">{user.display_name}</span>\n        <ChevronDown className={`w-4 h-4 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />\n      </button>\n\n      {isDropdownOpen && (\n        <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50\">\n          <div className=\"py-1\">\n            <div className=\"px-4 py-2 text-sm text-gray-500 border-b\">\n              <div className=\"font-medium text-gray-900\">{user.display_name}</div>\n              <div className=\"text-xs\">{user.email}</div>\n            </div>\n            \n            <button\n              onClick={handleProfileClick}\n              className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n            >\n              <User className=\"w-4 h-4 mr-3\" />\n              Profile\n            </button>\n            \n            <button\n              onClick={handleProfileClick}\n              className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n            >\n              <Settings className=\"w-4 h-4 mr-3\" />\n              Account Settings\n            </button>\n            \n            <div className=\"border-t\">\n              <button\n                onClick={handleSignOut}\n                disabled={isLoading}\n                className=\"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors disabled:opacity-50\"\n              >\n                {isLoading ? (\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-3\"></div>\n                ) : (\n                  <LogOut className=\"w-4 h-4 mr-3\" />\n                )}\n                {isLoading ? 'Signing out...' : 'Sign Out'}\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,SAAS,mBAAmB,KAAiB;gBAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;oBAC9E,kBAAkB;gBACpB;YACF;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;2CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;kCAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,aAAa;QACb,IAAI;YACF,MAAM;YACN,kBAAkB;YAClB,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB;QACzB,kBAAkB;QAClB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,IAAI,WAAW,CAAC,MAAM;QACpB,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;QAAW,KAAK;;0BAC7B,6LAAC;gBACC,SAAS,IAAM,kBAAkB,CAAC;gBAClC,WAAU;;oBAET,KAAK,UAAU,iBACd,6LAAC;wBACC,KAAK,KAAK,UAAU;wBACpB,KAAK,KAAK,YAAY;wBACtB,WAAU;;;;;6CAGZ,6LAAC;wBAAI,WAAU;kCACZ,YAAY,KAAK,YAAY;;;;;;kCAGlC,6LAAC;wBAAK,WAAU;kCAAmB,KAAK,YAAY;;;;;;kCACpD,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAW,CAAC,6BAA6B,EAAE,iBAAiB,eAAe,IAAI;;;;;;;;;;;;YAG7F,gCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAA6B,KAAK,YAAY;;;;;;8CAC7D,6LAAC;oCAAI,WAAU;8CAAW,KAAK,KAAK;;;;;;;;;;;;sCAGtC,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAInC,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAIvC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;;oCAET,0BACC,6LAAC;wCAAI,WAAU;;;;;6DAEf,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAEnB,YAAY,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD;GApHwB;;QACa,kIAAA,CAAA,UAAO;QAI3B,qIAAA,CAAA,YAAS;;;KALF", "debugId": null}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/layout/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { useAuth } from '@/contexts/AuthContext'\nimport LoginButton from '@/components/auth/LoginButton'\nimport AccountButton from '@/components/auth/AccountButton'\nimport { Crown, Home, User, Gamepad2 } from 'lucide-react'\n\nexport default function Navigation() {\n  const { user } = useAuth()\n\n  return (\n    <nav className=\"bg-white shadow-lg\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <Crown className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"text-xl font-bold text-gray-900\">ChessHub</span>\n            </Link>\n          </div>\n\n          <div className=\"flex items-center space-x-8\">\n            <Link\n              href=\"/\"\n              className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n            >\n              <Home className=\"w-4 h-4\" />\n              <span>Home</span>\n            </Link>\n\n            <Link\n              href=\"/demo\"\n              className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n            >\n              <Gamepad2 className=\"w-4 h-4\" />\n              <span>Demo</span>\n            </Link>\n\n            {user && (\n              <>\n                <Link\n                  href=\"/play\"\n                  className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  <Gamepad2 className=\"w-4 h-4\" />\n                  <span>Play</span>\n                </Link>\n                <Link\n                  href=\"/profile\"\n                  className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  <User className=\"w-4 h-4\" />\n                  <span>Profile</span>\n                </Link>\n              </>\n            )}\n\n            <LoginButton />\n            <AccountButton />\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;;;;;;kCAItD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAA<PERSON>,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,sMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;kDAAK;;;;;;;;;;;;0CAGR,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAK;;;;;;;;;;;;4BAGP,sBACC;;kDACE,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;;;0CAKZ,6LAAC,4IAAA,CAAA,UAAW;;;;;0CACZ,6LAAC,8IAAA,CAAA,UAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1B;GAzDwB;;QACL,kIAAA,CAAA,UAAO;;;KADF", "debugId": null}}, {"offset": {"line": 559, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/app/play/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { supabase } from '@/lib/supabase'\nimport Navigation from '@/components/layout/Navigation'\nimport ChessBoard from '@/components/chess/ChessBoard'\nimport { Plus, Users, Clock, Trophy } from 'lucide-react'\nimport Link from 'next/link'\n\ninterface Game {\n  id: string\n  white_player_id: string\n  black_player_id?: string\n  status: 'waiting' | 'active' | 'completed' | 'abandoned'\n  created_at: string\n  time_control: number\n  white_player?: {\n    display_name: string\n    avatar_url?: string\n  }\n  black_player?: {\n    display_name: string\n    avatar_url?: string\n  }\n}\n\nexport default function PlayPage() {\n  const { user } = useAuth()\n  const [userGames, setUserGames] = useState<Game[]>([])\n  const [availableGames, setAvailableGames] = useState<Game[]>([])\n  const [loading, setLoading] = useState(true)\n  const [creating, setCreating] = useState(false)\n  const [joiningGameId, setJoiningGameId] = useState<string | null>(null)\n\n  useEffect(() => {\n    if (user) {\n      console.log('User loaded, fetching games for:', user)\n      fetchGames()\n\n      // Also verify the user exists in the database\n      verifyUserInDatabase()\n\n      // Set up real-time subscriptions\n      setupRealtimeSubscriptions()\n    }\n\n    // Cleanup subscriptions on unmount\n    return () => {\n      supabase.removeAllChannels()\n    }\n  }, [user])\n\n  const setupRealtimeSubscriptions = () => {\n    if (!user) return\n\n    // Subscribe to games table changes\n    const gamesSubscription = supabase\n      .channel('games-changes')\n      .on(\n        'postgres_changes',\n        {\n          event: '*',\n          schema: 'public',\n          table: 'games'\n        },\n        (payload) => {\n          console.log('Games table change:', payload)\n\n          // Handle different types of changes\n          if (payload.eventType === 'INSERT') {\n            // New game created - refresh available games\n            console.log('New game created:', payload.new)\n            fetchGames()\n          } else if (payload.eventType === 'UPDATE') {\n            // Game updated (e.g., someone joined) - refresh both lists\n            console.log('Game updated:', payload.new)\n            fetchGames()\n          } else if (payload.eventType === 'DELETE') {\n            // Game deleted - refresh both lists\n            console.log('Game deleted:', payload.old)\n            fetchGames()\n          }\n        }\n      )\n      .subscribe((status) => {\n        console.log('Games subscription status:', status)\n      })\n\n    return () => {\n      gamesSubscription.unsubscribe()\n    }\n  }\n\n  const verifyUserInDatabase = async () => {\n    if (!user || !supabase) return\n\n    try {\n      const { data, error } = await supabase\n        .from('users')\n        .select('*')\n        .eq('id', user.id)\n        .single()\n\n      if (error) {\n        console.error('User verification error:', error)\n        console.log('User may not exist in database, this could cause game creation to fail')\n      } else {\n        console.log('User verified in database:', data)\n      }\n    } catch (error) {\n      console.error('Error verifying user:', error)\n    }\n  }\n\n  const fetchGames = async () => {\n    if (!user) return\n\n    try {\n      // Fetch user's games (games they're part of)\n      const { data: userGamesData, error: userGamesError } = await supabase\n        .from('games')\n        .select(`\n          *,\n          white_player:users!games_white_player_id_fkey(display_name, avatar_url),\n          black_player:users!games_black_player_id_fkey(display_name, avatar_url)\n        `)\n        .or(`white_player_id.eq.${user.id},black_player_id.eq.${user.id}`)\n        .order('created_at', { ascending: false })\n\n      if (userGamesError) throw userGamesError\n\n      // Fetch available games (waiting games that user can join)\n      const { data: availableGamesData, error: availableGamesError } = await supabase\n        .from('games')\n        .select(`\n          *,\n          white_player:users!games_white_player_id_fkey(display_name, avatar_url),\n          black_player:users!games_black_player_id_fkey(display_name, avatar_url)\n        `)\n        .eq('status', 'waiting')\n        .is('black_player_id', null)\n        .neq('white_player_id', user.id) // Exclude user's own games\n        .order('created_at', { ascending: false })\n\n      if (availableGamesError) throw availableGamesError\n\n      setUserGames(userGamesData || [])\n      setAvailableGames(availableGamesData || [])\n    } catch (error) {\n      console.error('Error fetching games:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const createGame = async () => {\n    if (!user) {\n      console.error('No user found when trying to create game')\n      alert('You must be signed in to create a game. Please sign in first.')\n      return\n    }\n\n    console.log('Creating game for user:', user)\n    console.log('User ID:', user.id)\n    console.log('Supabase client:', supabase)\n\n    setCreating(true)\n\n    try {\n      // First, let's check if the games table exists by trying a simple query\n      console.log('Checking if games table exists...')\n      const { data: testData, error: testError } = await supabase\n        .from('games')\n        .select('count', { count: 'exact', head: true })\n\n      if (testError) {\n        console.error('Games table error:', {\n          message: testError.message,\n          details: testError.details,\n          hint: testError.hint,\n          code: testError.code\n        })\n        alert(`Database setup incomplete. Error: ${testError.message}. Please run the database setup in your Supabase dashboard.`)\n        return\n      }\n\n      console.log('Games table exists, proceeding with game creation...')\n      console.log('Inserting game with data:', {\n        white_player_id: user.id,\n        status: 'waiting'\n      })\n\n      const { data, error } = await supabase\n        .from('games')\n        .insert({\n          white_player_id: user.id,\n          status: 'waiting'\n        })\n        .select()\n        .single()\n\n      if (error) {\n        console.error('Game creation error:', {\n          message: error.message,\n          details: error.details,\n          hint: error.hint,\n          code: error.code,\n          user_id: user.id\n        })\n\n        // Provide specific error messages based on error codes\n        if (error.code === '23503') {\n          alert('Foreign key constraint error: Your user profile may not exist in the database. Please sign out and sign in again.')\n        } else if (error.code === '42501') {\n          alert('Permission denied: Row Level Security policies may be blocking game creation. Please check the database setup.')\n        } else {\n          alert(`Failed to create game: ${error.message}`)\n        }\n        return\n      }\n\n      console.log('Game created successfully:', data)\n      // Redirect to the game\n      window.location.href = `/game/${data.id}`\n    } catch (error) {\n      console.error('Unexpected error creating game:', error)\n      alert(`Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`)\n    } finally {\n      setCreating(false)\n    }\n  }\n\n  const joinGame = async (gameId: string) => {\n    if (!user) return\n\n    // Find the game to show confirmation dialog\n    const game = availableGames.find(g => g.id === gameId)\n    if (!game) return\n\n    // Show confirmation dialog\n    const confirmed = window.confirm(\n      `Join game against ${game.white_player?.display_name}?\\n\\n` +\n      `Time control: ${Math.floor(game.time_control / 60)} minutes\\n` +\n      `You will play as Black.`\n    )\n\n    if (!confirmed) return\n\n    setJoiningGameId(gameId)\n\n    try {\n      // First, verify the game is still available\n      const { data: gameData, error: fetchError } = await supabase\n        .from('games')\n        .select(`\n          *,\n          white_player:users!games_white_player_id_fkey(display_name)\n        `)\n        .eq('id', gameId)\n        .single()\n\n      if (fetchError) {\n        throw new Error('Game not found or no longer available.')\n      }\n\n      // Check if game is still available\n      if (gameData.status !== 'waiting') {\n        throw new Error('This game has already started or finished.')\n      }\n\n      if (gameData.black_player_id !== null) {\n        throw new Error('This game already has a second player.')\n      }\n\n      // Check if user is trying to join their own game\n      if (gameData.white_player_id === user.id) {\n        throw new Error('You cannot join your own game.')\n      }\n\n      // Join the game with atomic update\n      const { data: updateData, error: updateError } = await supabase\n        .from('games')\n        .update({\n          black_player_id: user.id,\n          status: 'active'\n        })\n        .eq('id', gameId)\n        .eq('status', 'waiting') // Additional safety check\n        .is('black_player_id', null) // Additional safety check\n        .select()\n\n      if (updateError) {\n        throw new Error('Failed to join game. It may have been taken by another player.')\n      }\n\n      if (!updateData || updateData.length === 0) {\n        throw new Error('Game is no longer available. Another player may have joined first.')\n      }\n\n      console.log('Successfully joined game:', updateData[0])\n\n      // Redirect to the game\n      window.location.href = `/game/${gameId}`\n    } catch (error) {\n      console.error('Error joining game:', error)\n      const errorMessage = error instanceof Error ? error.message : 'Failed to join game. Please try again.'\n      alert(errorMessage)\n\n      // Refresh games list to show current state\n      fetchGames()\n    } finally {\n      setJoiningGameId(null)\n    }\n  }\n\n  if (!user) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navigation />\n        <div className=\"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-3xl font-extrabold text-gray-900 mb-4\">\n              Sign in to Play Chess\n            </h1>\n            <p className=\"text-lg text-gray-600\">\n              You need to be signed in to create or join games.\n            </p>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navigation />\n      \n      <div className=\"max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-extrabold text-gray-900 mb-2\">Play Chess</h1>\n          <p className=\"text-lg text-gray-600\">Create a new game or join an existing one</p>\n        </div>\n\n        <div className=\"grid grid-cols-1 xl:grid-cols-4 gap-8\">\n          {/* Game Actions */}\n          <div className=\"xl:col-span-1\">\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Quick Actions</h2>\n\n              <button\n                onClick={createGame}\n                disabled={creating}\n                className=\"w-full inline-flex items-center justify-center px-4 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed mb-4\"\n              >\n                {creating ? (\n                  <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"></div>\n                ) : (\n                  <>\n                    <Plus className=\"h-5 w-5 mr-2\" />\n                    Create New Game\n                  </>\n                )}\n              </button>\n\n              <div className=\"border-t pt-4\">\n                <h3 className=\"text-sm font-medium text-gray-900 mb-2\">Game Statistics</h3>\n                <div className=\"space-y-2 text-sm text-gray-600\">\n                  <div className=\"flex items-center\">\n                    <Trophy className=\"h-4 w-4 mr-2\" />\n                    <span>Games Played: {user.games_played || 0}</span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <Users className=\"h-4 w-4 mr-2\" />\n                    <span>Rating: {user.rating || 1200}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Available Games */}\n          <div className=\"xl:col-span-1.5\">\n            <div className=\"bg-white rounded-lg shadow-md\">\n              <div className=\"px-6 py-4 border-b border-gray-200\">\n                <h2 className=\"text-xl font-semibold text-gray-900 flex items-center\">\n                  <Users className=\"h-5 w-5 mr-2 text-green-600\" />\n                  Available Games\n                  {availableGames.length > 0 && (\n                    <span className=\"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                      {availableGames.length}\n                    </span>\n                  )}\n                </h2>\n              </div>\n\n              <div className=\"divide-y divide-gray-200 max-h-96 overflow-y-auto\">\n                {loading ? (\n                  <div className=\"p-6 text-center\">\n                    <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto\"></div>\n                    <p className=\"mt-2 text-gray-600\">Loading available games...</p>\n                  </div>\n                ) : availableGames.length === 0 ? (\n                  <div className=\"p-6 text-center text-gray-500\">\n                    <Users className=\"h-12 w-12 mx-auto mb-4 text-gray-400\" />\n                    <p>No games available to join.</p>\n                    <p className=\"text-sm mt-1\">Create a game to get started!</p>\n                  </div>\n                ) : (\n                  availableGames.map((game) => (\n                    <div key={game.id} className=\"p-4 hover:bg-gray-50\">\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center space-x-3\">\n                          <div className=\"flex-shrink-0\">\n                            <div className=\"w-3 h-3 rounded-full bg-green-400\"></div>\n                          </div>\n\n                          <div>\n                            <div className=\"flex items-center space-x-2\">\n                              <span className=\"font-medium text-gray-900\">\n                                {game.white_player?.display_name}\n                              </span>\n                              <span className=\"text-gray-500 text-sm\">waiting for opponent</span>\n                            </div>\n                            <div className=\"flex items-center space-x-3 mt-1 text-sm text-gray-500\">\n                              <span className=\"flex items-center\">\n                                <Clock className=\"h-3 w-3 mr-1\" />\n                                {Math.floor(game.time_control / 60)}min\n                              </span>\n                              <span>\n                                {new Date(game.created_at).toLocaleTimeString([], {\n                                  hour: '2-digit',\n                                  minute: '2-digit'\n                                })}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n\n                        <button\n                          onClick={() => joinGame(game.id)}\n                          disabled={joiningGameId === game.id}\n                          className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n                        >\n                          {joiningGameId === game.id ? (\n                            <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n                          ) : (\n                            'Join'\n                          )}\n                        </button>\n                      </div>\n                    </div>\n                  ))\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Your Games */}\n          <div className=\"xl:col-span-1.5\">\n            <div className=\"bg-white rounded-lg shadow-md\">\n              <div className=\"px-6 py-4 border-b border-gray-200\">\n                <h2 className=\"text-xl font-semibold text-gray-900 flex items-center\">\n                  <Trophy className=\"h-5 w-5 mr-2 text-blue-600\" />\n                  Your Games\n                  {userGames.length > 0 && (\n                    <span className=\"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                      {userGames.length}\n                    </span>\n                  )}\n                </h2>\n              </div>\n\n              <div className=\"divide-y divide-gray-200 max-h-96 overflow-y-auto\">\n                {loading ? (\n                  <div className=\"p-6 text-center\">\n                    <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"></div>\n                    <p className=\"mt-2 text-gray-600\">Loading your games...</p>\n                  </div>\n                ) : userGames.length === 0 ? (\n                  <div className=\"p-6 text-center text-gray-500\">\n                    <Trophy className=\"h-12 w-12 mx-auto mb-4 text-gray-400\" />\n                    <p>No games yet. Create your first game!</p>\n                  </div>\n                ) : (\n                  userGames.map((game) => (\n                    <div key={game.id} className=\"p-4 hover:bg-gray-50\">\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center space-x-3\">\n                          <div className=\"flex-shrink-0\">\n                            <div className={`w-3 h-3 rounded-full ${\n                              game.status === 'waiting' ? 'bg-yellow-400' :\n                              game.status === 'active' ? 'bg-green-400' :\n                              'bg-gray-400'\n                            }`}></div>\n                          </div>\n\n                          <div>\n                            <div className=\"flex items-center space-x-2\">\n                              <span className=\"font-medium text-gray-900\">\n                                {game.white_player?.display_name}\n                              </span>\n                              <span className=\"text-gray-500\">vs</span>\n                              <span className=\"font-medium text-gray-900\">\n                                {game.black_player?.display_name || 'Waiting...'}\n                              </span>\n                            </div>\n                            <div className=\"flex items-center space-x-3 mt-1 text-sm text-gray-500\">\n                              <span className=\"capitalize\">{game.status}</span>\n                              <span className=\"flex items-center\">\n                                <Clock className=\"h-3 w-3 mr-1\" />\n                                {new Date(game.created_at).toLocaleDateString()}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n\n                        <Link\n                          href={`/game/${game.id}`}\n                          className=\"inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\"\n                        >\n                          {game.status === 'active' ? 'Continue' : 'View'}\n                        </Link>\n                      </div>\n                    </div>\n                  ))\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AACA;;;AARA;;;;;;;AA2Be,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,MAAM;gBACR,QAAQ,GAAG,CAAC,oCAAoC;gBAChD;gBAEA,8CAA8C;gBAC9C;gBAEA,iCAAiC;gBACjC;YACF;YAEA,mCAAmC;YACnC;sCAAO;oBACL,yHAAA,CAAA,WAAQ,CAAC,iBAAiB;gBAC5B;;QACF;6BAAG;QAAC;KAAK;IAET,MAAM,6BAA6B;QACjC,IAAI,CAAC,MAAM;QAEX,mCAAmC;QACnC,MAAM,oBAAoB,yHAAA,CAAA,WAAQ,CAC/B,OAAO,CAAC,iBACR,EAAE,CACD,oBACA;YACE,OAAO;YACP,QAAQ;YACR,OAAO;QACT,GACA,CAAC;YACC,QAAQ,GAAG,CAAC,uBAAuB;YAEnC,oCAAoC;YACpC,IAAI,QAAQ,SAAS,KAAK,UAAU;gBAClC,6CAA6C;gBAC7C,QAAQ,GAAG,CAAC,qBAAqB,QAAQ,GAAG;gBAC5C;YACF,OAAO,IAAI,QAAQ,SAAS,KAAK,UAAU;gBACzC,2DAA2D;gBAC3D,QAAQ,GAAG,CAAC,iBAAiB,QAAQ,GAAG;gBACxC;YACF,OAAO,IAAI,QAAQ,SAAS,KAAK,UAAU;gBACzC,oCAAoC;gBACpC,QAAQ,GAAG,CAAC,iBAAiB,QAAQ,GAAG;gBACxC;YACF;QACF,GAED,SAAS,CAAC,CAAC;YACV,QAAQ,GAAG,CAAC,8BAA8B;QAC5C;QAEF,OAAO;YACL,kBAAkB,WAAW;QAC/B;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,QAAQ,CAAC,yHAAA,CAAA,WAAQ,EAAE;QAExB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,QAAQ,GAAG,CAAC;YACd,OAAO;gBACL,QAAQ,GAAG,CAAC,8BAA8B;YAC5C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,6CAA6C;YAC7C,MAAM,EAAE,MAAM,aAAa,EAAE,OAAO,cAAc,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAClE,IAAI,CAAC,SACL,MAAM,CAAC,CAAC;;;;QAIT,CAAC,EACA,EAAE,CAAC,CAAC,mBAAmB,EAAE,KAAK,EAAE,CAAC,oBAAoB,EAAE,KAAK,EAAE,EAAE,EAChE,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,gBAAgB,MAAM;YAE1B,2DAA2D;YAC3D,MAAM,EAAE,MAAM,kBAAkB,EAAE,OAAO,mBAAmB,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC5E,IAAI,CAAC,SACL,MAAM,CAAC,CAAC;;;;QAIT,CAAC,EACA,EAAE,CAAC,UAAU,WACb,EAAE,CAAC,mBAAmB,MACtB,GAAG,CAAC,mBAAmB,KAAK,EAAE,EAAE,2BAA2B;aAC3D,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,qBAAqB,MAAM;YAE/B,aAAa,iBAAiB,EAAE;YAChC,kBAAkB,sBAAsB,EAAE;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,MAAM;YACT,QAAQ,KAAK,CAAC;YACd,MAAM;YACN;QACF;QAEA,QAAQ,GAAG,CAAC,2BAA2B;QACvC,QAAQ,GAAG,CAAC,YAAY,KAAK,EAAE;QAC/B,QAAQ,GAAG,CAAC,oBAAoB,yHAAA,CAAA,WAAQ;QAExC,YAAY;QAEZ,IAAI;YACF,wEAAwE;YACxE,QAAQ,GAAG,CAAC;YACZ,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACxD,IAAI,CAAC,SACL,MAAM,CAAC,SAAS;gBAAE,OAAO;gBAAS,MAAM;YAAK;YAEhD,IAAI,WAAW;gBACb,QAAQ,KAAK,CAAC,sBAAsB;oBAClC,SAAS,UAAU,OAAO;oBAC1B,SAAS,UAAU,OAAO;oBAC1B,MAAM,UAAU,IAAI;oBACpB,MAAM,UAAU,IAAI;gBACtB;gBACA,MAAM,CAAC,kCAAkC,EAAE,UAAU,OAAO,CAAC,2DAA2D,CAAC;gBACzH;YACF;YAEA,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,6BAA6B;gBACvC,iBAAiB,KAAK,EAAE;gBACxB,QAAQ;YACV;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC;gBACN,iBAAiB,KAAK,EAAE;gBACxB,QAAQ;YACV,GACC,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,wBAAwB;oBACpC,SAAS,MAAM,OAAO;oBACtB,SAAS,MAAM,OAAO;oBACtB,MAAM,MAAM,IAAI;oBAChB,MAAM,MAAM,IAAI;oBAChB,SAAS,KAAK,EAAE;gBAClB;gBAEA,uDAAuD;gBACvD,IAAI,MAAM,IAAI,KAAK,SAAS;oBAC1B,MAAM;gBACR,OAAO,IAAI,MAAM,IAAI,KAAK,SAAS;oBACjC,MAAM;gBACR,OAAO;oBACL,MAAM,CAAC,uBAAuB,EAAE,MAAM,OAAO,EAAE;gBACjD;gBACA;YACF;YAEA,QAAQ,GAAG,CAAC,8BAA8B;YAC1C,uBAAuB;YACvB,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM,CAAC,kBAAkB,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QACvF,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI,CAAC,MAAM;QAEX,4CAA4C;QAC5C,MAAM,OAAO,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC/C,IAAI,CAAC,MAAM;QAEX,2BAA2B;QAC3B,MAAM,YAAY,OAAO,OAAO,CAC9B,CAAC,kBAAkB,EAAE,KAAK,YAAY,EAAE,aAAa,KAAK,CAAC,GAC3D,CAAC,cAAc,EAAE,KAAK,KAAK,CAAC,KAAK,YAAY,GAAG,IAAI,UAAU,CAAC,GAC/D,CAAC,uBAAuB,CAAC;QAG3B,IAAI,CAAC,WAAW;QAEhB,iBAAiB;QAEjB,IAAI;YACF,4CAA4C;YAC5C,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACzD,IAAI,CAAC,SACL,MAAM,CAAC,CAAC;;;QAGT,CAAC,EACA,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,YAAY;gBACd,MAAM,IAAI,MAAM;YAClB;YAEA,mCAAmC;YACnC,IAAI,SAAS,MAAM,KAAK,WAAW;gBACjC,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,SAAS,eAAe,KAAK,MAAM;gBACrC,MAAM,IAAI,MAAM;YAClB;YAEA,iDAAiD;YACjD,IAAI,SAAS,eAAe,KAAK,KAAK,EAAE,EAAE;gBACxC,MAAM,IAAI,MAAM;YAClB;YAEA,mCAAmC;YACnC,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC5D,IAAI,CAAC,SACL,MAAM,CAAC;gBACN,iBAAiB,KAAK,EAAE;gBACxB,QAAQ;YACV,GACC,EAAE,CAAC,MAAM,QACT,EAAE,CAAC,UAAU,WAAW,0BAA0B;aAClD,EAAE,CAAC,mBAAmB,MAAM,0BAA0B;aACtD,MAAM;YAET,IAAI,aAAa;gBACf,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,cAAc,WAAW,MAAM,KAAK,GAAG;gBAC1C,MAAM,IAAI,MAAM;YAClB;YAEA,QAAQ,GAAG,CAAC,6BAA6B,UAAU,CAAC,EAAE;YAEtD,uBAAuB;YACvB,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,MAAM,EAAE,QAAQ;QAC1C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,MAAM;YAEN,2CAA2C;YAC3C;QACF,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6IAAA,CAAA,UAAU;;;;;8BACX,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6C;;;;;;0CAG3D,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;IAO/C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6IAAA,CAAA,UAAU;;;;;0BAEX,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6C;;;;;;0CAC3D,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAGvC,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAEzD,6LAAC;4CACC,SAAS;4CACT,UAAU;4CACV,WAAU;sDAET,yBACC,6LAAC;gDAAI,WAAU;;;;;qEAEf;;kEACE,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;sDAMvC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,yMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,6LAAC;;wEAAK;wEAAe,KAAK,YAAY,IAAI;;;;;;;;;;;;;sEAE5C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,6LAAC;;wEAAK;wEAAS,KAAK,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQxC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAgC;oDAEhD,eAAe,MAAM,GAAG,mBACvB,6LAAC;wDAAK,WAAU;kEACb,eAAe,MAAM;;;;;;;;;;;;;;;;;sDAM9B,6LAAC;4CAAI,WAAU;sDACZ,wBACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAE,WAAU;kEAAqB;;;;;;;;;;;uDAElC,eAAe,MAAM,KAAK,kBAC5B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;kEAAE;;;;;;kEACH,6LAAC;wDAAE,WAAU;kEAAe;;;;;;;;;;;uDAG9B,eAAe,GAAG,CAAC,CAAC,qBAClB,6LAAC;oDAAkB,WAAU;8DAC3B,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;;;;;;;;;;;kFAGjB,6LAAC;;0FACC,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAK,WAAU;kGACb,KAAK,YAAY,EAAE;;;;;;kGAEtB,6LAAC;wFAAK,WAAU;kGAAwB;;;;;;;;;;;;0FAE1C,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAK,WAAU;;0GACd,6LAAC,uMAAA,CAAA,QAAK;gGAAC,WAAU;;;;;;4FAChB,KAAK,KAAK,CAAC,KAAK,YAAY,GAAG;4FAAI;;;;;;;kGAEtC,6LAAC;kGACE,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC,EAAE,EAAE;4FAChD,MAAM;4FACN,QAAQ;wFACV;;;;;;;;;;;;;;;;;;;;;;;;0EAMR,6LAAC;gEACC,SAAS,IAAM,SAAS,KAAK,EAAE;gEAC/B,UAAU,kBAAkB,KAAK,EAAE;gEACnC,WAAU;0EAET,kBAAkB,KAAK,EAAE,iBACxB,6LAAC;oEAAI,WAAU;;;;;2EAEf;;;;;;;;;;;;mDArCE,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;0CAiD3B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAA+B;oDAEhD,UAAU,MAAM,GAAG,mBAClB,6LAAC;wDAAK,WAAU;kEACb,UAAU,MAAM;;;;;;;;;;;;;;;;;sDAMzB,6LAAC;4CAAI,WAAU;sDACZ,wBACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAE,WAAU;kEAAqB;;;;;;;;;;;uDAElC,UAAU,MAAM,KAAK,kBACvB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;kEAAE;;;;;;;;;;;uDAGL,UAAU,GAAG,CAAC,CAAC,qBACb,6LAAC;oDAAkB,WAAU;8DAC3B,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAW,CAAC,qBAAqB,EACpC,KAAK,MAAM,KAAK,YAAY,kBAC5B,KAAK,MAAM,KAAK,WAAW,iBAC3B,eACA;;;;;;;;;;;kFAGJ,6LAAC;;0FACC,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAK,WAAU;kGACb,KAAK,YAAY,EAAE;;;;;;kGAEtB,6LAAC;wFAAK,WAAU;kGAAgB;;;;;;kGAChC,6LAAC;wFAAK,WAAU;kGACb,KAAK,YAAY,EAAE,gBAAgB;;;;;;;;;;;;0FAGxC,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAK,WAAU;kGAAc,KAAK,MAAM;;;;;;kGACzC,6LAAC;wFAAK,WAAU;;0GACd,6LAAC,uMAAA,CAAA,QAAK;gGAAC,WAAU;;;;;;4FAChB,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;0EAMrD,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gEACxB,WAAU;0EAET,KAAK,MAAM,KAAK,WAAW,aAAa;;;;;;;;;;;;mDAnCrC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDrC;GA3fwB;;QACL,kIAAA,CAAA,UAAO;;;KADF", "debugId": null}}]}